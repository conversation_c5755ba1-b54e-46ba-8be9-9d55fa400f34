name: CI

on:
  push:
    branches:
      - master
      - main
  pull_request:
    branches:
      - master
      - main

jobs:
  build:
    name: Build, lint and unit tests
    runs-on: ubuntu-latest
    permissions:
      contents: read
    outputs:
      plugin-id: ${{ steps.metadata.outputs.plugin-id }}
      plugin-version: ${{ steps.metadata.outputs.plugin-version }}
      has-e2e: ${{ steps.check-for-e2e.outputs.has-e2e }}
      has-backend: ${{ steps.check-for-backend.outputs.has-backend }}
    env:
      GRAFANA_ACCESS_POLICY_TOKEN: ${{ secrets.GRAFANA_ACCESS_POLICY_TOKEN }}
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check types
        run: npm run typecheck
      - name: Lint
        run: npm run lint
      - name: Unit tests
        run: npm run test:ci
      - name: Build frontend
        run: npm run build

      - name: Check for backend
        id: check-for-backend
        run: |
          if [ -f "Magefile.go" ]
          then
            echo "has-backend=true" >> $GITHUB_OUTPUT
          fi

      - name: Setup Go environment
        if: steps.check-for-backend.outputs.has-backend == 'true'
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: Test backend
        if: steps.check-for-backend.outputs.has-backend == 'true'
        uses: magefile/mage-action@6f50bbb8ea47d56e62dee92392788acbc8192d0b # v3.1.0
        with:
          version: latest
          args: coverage

      - name: Build backend
        if: steps.check-for-backend.outputs.has-backend == 'true'
        uses: magefile/mage-action@6f50bbb8ea47d56e62dee92392788acbc8192d0b # v3.1.0
        with:
          version: latest
          args: buildAll

      - name: Check for E2E
        id: check-for-e2e
        run: |
          if [ -f "playwright.config.ts" ]
          then
            echo "has-e2e=true" >> $GITHUB_OUTPUT
          fi

      - name: Sign plugin
        run: npm run sign
        if: ${{ env.GRAFANA_ACCESS_POLICY_TOKEN != '' }}

      - name: Get plugin metadata
        id: metadata
        run: |
          sudo apt-get install jq

          export GRAFANA_PLUGIN_ID=$(cat dist/plugin.json | jq -r .id)
          export GRAFANA_PLUGIN_VERSION=$(cat dist/plugin.json | jq -r .info.version)
          export GRAFANA_PLUGIN_ARTIFACT=${GRAFANA_PLUGIN_ID}-${GRAFANA_PLUGIN_VERSION}.zip

          echo "plugin-id=${GRAFANA_PLUGIN_ID}" >> $GITHUB_OUTPUT
          echo "plugin-version=${GRAFANA_PLUGIN_VERSION}" >> $GITHUB_OUTPUT
          echo "archive=${GRAFANA_PLUGIN_ARTIFACT}" >> $GITHUB_OUTPUT

      - name: Package plugin
        id: package-plugin
        run: |
          mv dist ${PLUGIN_ID}
          zip ${ARCHIVE} ${PLUGIN_ID} -r
        env:
          ARCHIVE: ${{ steps.metadata.outputs.archive }}
          PLUGIN_ID: ${{ steps.metadata.outputs.plugin-id }}

      - name: Check plugin.json
        run: |
          docker run --pull=always \
            -v $PWD/${ARCHIVE}:/archive.zip \
            grafana/plugin-validator-cli -analyzer=metadatavalid /archive.zip
        env:
          ARCHIVE: ${{ steps.metadata.outputs.archive }}

      - name: Archive Build
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.metadata.outputs.plugin-id }}-${{ steps.metadata.outputs.plugin-version }}
          path: ${{ steps.metadata.outputs.plugin-id }}
          retention-days: 5

  resolve-versions:
    name: Resolve e2e images
    runs-on: ubuntu-latest
    permissions:
      contents: read
    timeout-minutes: 3
    needs: build
    if: ${{ needs.build.outputs.has-e2e == 'true' }}
    outputs:
      matrix: ${{ steps.resolve-versions.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Resolve Grafana E2E versions
        id: resolve-versions
        uses: grafana/plugin-actions/e2e-version@main # zizmor: ignore[unpinned-uses] provided by grafana

  playwright-tests:
    needs: [resolve-versions, build]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
      pull-requests: write
    strategy:
      fail-fast: false
      matrix:
        GRAFANA_IMAGE: ${{fromJson(needs.resolve-versions.outputs.matrix)}}
    name: e2e test ${{ matrix.GRAFANA_IMAGE.name }}@${{ matrix.GRAFANA_IMAGE.VERSION }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Download plugin
        uses: actions/download-artifact@v4
        with:
          path: dist
          name: ${{ needs.build.outputs.plugin-id }}-${{ needs.build.outputs.plugin-version }}

      - name: Execute permissions on binary
        if: needs.build.outputs.has-backend == 'true'
        run: |
          chmod +x ./dist/gpx_*

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dev dependencies
        run: npm ci

      - name: Start Grafana
        run: |
          docker compose pull
          ANONYMOUS_AUTH_ENABLED=false DEVELOPMENT=false GRAFANA_VERSION=${{ matrix.GRAFANA_IMAGE.VERSION }} GRAFANA_IMAGE=${{ matrix.GRAFANA_IMAGE.NAME }} docker compose up -d

      - name: Wait for grafana server
        uses: grafana/plugin-actions/wait-for-grafana@main # zizmor: ignore[unpinned-uses] provided by grafana
        with:
          url: http://localhost:3000/login

      - name: Install Playwright Browsers
        run: npm exec playwright install chromium --with-deps

      - name: Run Playwright tests
        id: run-tests
        run: npm run e2e

      - name: Upload e2e test summary
        uses: grafana/plugin-actions/playwright-gh-pages/upload-report-artifacts@main # zizmor: ignore[unpinned-uses] provided by grafana
        if: ${{ always() && !cancelled() }}
        with:
          upload-report: false
          github-token: ${{ secrets.GITHUB_TOKEN }}
          test-outcome: ${{ steps.run-tests.outcome }}

      - name: Docker logs
        if: ${{ always() && steps.run-tests.outcome == 'failure' }}
        run: |
          docker logs ces-voltageband-panel >& grafana-server.log

      - name: Stop grafana docker
        run: docker compose down

      # Uncomment this step to upload the server log to Github artifacts. Remember Github artifacts are public on the Internet if the repository is public.
      # - name: Upload server log
      #   uses: actions/upload-artifact@v4
      #   if: ${{ always() && steps.run-tests.outcome == 'failure' }}
      #   with:
      #     name: ${{ matrix.GRAFANA_IMAGE.NAME }}-v${{ matrix.GRAFANA_IMAGE.VERSION }}-${{github.run_id}}-server-log
      #     path: grafana-server.log
      #     retention-days: 5

  publish-report:
    if: ${{ always() && !cancelled() }}
    permissions:
      contents: write
      id-token: write
      pull-requests: write
    needs: [playwright-tests]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          # required for playwright-gh-pages
          persist-credentials: true
      - name: Publish report
        uses: grafana/plugin-actions/playwright-gh-pages/deploy-report-pages@main # zizmor: ignore[unpinned-uses] provided by grafana
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
