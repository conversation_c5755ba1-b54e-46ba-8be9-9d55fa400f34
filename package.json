{"name": "voltage-band", "version": "1.0.0", "scripts": {"build": "webpack -c ./.config/webpack/webpack.config.ts --env production", "dev": "webpack -w -c ./.config/webpack/webpack.config.ts --env development", "test": "jest --watch --only<PERSON><PERSON>ed", "test:ci": "jest --passWithNoTests --maxWorkers 4", "typecheck": "tsc --noEmit", "lint": "eslint --cache --ignore-path ./.gitignore --ext .js,.jsx,.ts,.tsx .", "lint:fix": "npm run lint -- --fix && prettier --write --list-different .", "e2e": "playwright test", "server": "docker compose up --build", "sign": "npx --yes @grafana/sign-plugin@latest"}, "author": "<PERSON><PERSON>", "license": "Apache-2.0", "devDependencies": {"@grafana/eslint-config": "^8.0.0", "@grafana/plugin-e2e": "^2.1.7", "@grafana/tsconfig": "^2.0.0", "@playwright/test": "^1.52.0", "@stylistic/eslint-plugin-ts": "^2.9.0", "@swc/core": "^1.3.90", "@swc/helpers": "^0.5.0", "@swc/jest": "^0.2.26", "@testing-library/jest-dom": "6.1.4", "@testing-library/react": "14.0.0", "@types/jest": "^29.5.0", "@types/node": "^20.8.7", "@types/testing-library__jest-dom": "5.14.8", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.3", "eslint": "^8.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jsdoc": "^46.8.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-webpack-plugin": "^4.0.1", "fork-ts-checker-webpack-plugin": "^8.0.0", "glob": "^10.2.7", "identity-obj-proxy": "3.0.0", "imports-loader": "^5.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.7", "replace-in-file-webpack-plugin": "^1.0.6", "sass": "1.63.2", "sass-loader": "13.3.1", "semver": "^7.6.3", "style-loader": "3.3.3", "swc-loader": "^0.2.3", "terser-webpack-plugin": "^5.3.10", "ts-node": "^10.9.2", "typescript": "5.5.4", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-livereload-plugin": "^3.0.2", "webpack-subresource-integrity": "^5.1.0", "webpack-virtual-modules": "^0.6.2"}, "engines": {"node": ">=22"}, "dependencies": {"@emotion/css": "11.10.6", "@grafana/data": "^12.1.0", "@grafana/runtime": "^12.1.0", "@grafana/ui": "^12.1.0", "@grafana/schema": "^12.1.0", "react": "18.2.0", "react-dom": "18.2.0"}, "packageManager": "npm@10.9.0"}