services:
  grafana:
    user: root
    container_name: 'ces-voltageband-panel'

    build:
      context: .
      args:
        grafana_image: ${GRAFANA_IMAGE:-grafana}
        grafana_version: ${GRAFANA_VERSION:-12.2.0-16818804881-ubuntu}
        development: ${DEVELOPMENT:-false}
        anonymous_auth_enabled: ${ANONYMOUS_AUTH_ENABLED:-true}
    ports:
      - 3000:3000/tcp
    volumes:
      - ../dist:/var/lib/grafana/plugins/ces-voltageband-panel
      - ../provisioning:/etc/grafana/provisioning
      - ..:/root/ces-voltageband-panel

    environment:
      NODE_ENV: development
      GF_LOG_FILTERS: plugin.ces-voltageband-panel:debug
      GF_LOG_LEVEL: debug
      GF_DATAPROXY_LOGGING: 1
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: ces-voltageband-panel
